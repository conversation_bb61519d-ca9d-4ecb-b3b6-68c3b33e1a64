import React from 'react';

const KitchenGallerySimple = () => {
  return (
    <section id="kitchens" className="py-24 bg-gradient-to-br from-slate-50 via-white to-blue-50">
      <div className="container mx-auto px-6">
        <div className="text-center mb-16">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-orange-500 to-red-500 rounded-2xl mb-6 shadow-lg">
            <i className="ri-restaurant-line text-2xl text-white"></i>
          </div>
          <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold bg-gradient-to-r from-gray-800 via-gray-900 to-gray-800 bg-clip-text text-transparent mb-6">
            معرض
            <span className="block bg-gradient-to-r from-orange-600 via-red-600 to-pink-600 bg-clip-text text-transparent">
              المطابخ الفاخرة
            </span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            استكشف مجموعتنا المتنوعة من تصاميم المطابخ العصرية والكلاسيكية التي تناسب مختلف الأذواق والمساحات
          </p>
          <div className="w-24 h-1 bg-gradient-to-r from-orange-500 to-red-500 mx-auto mt-8 rounded-full"></div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {[1, 2, 3, 4, 5, 6].map((item) => (
            <div key={item} className="bg-white rounded-3xl overflow-hidden shadow-xl hover:shadow-2xl transition-all duration-500">
              <div className="relative h-64 overflow-hidden">
                <img
                  src={`https://readdy.ai/api/search-image?query=modern%20kitchen%20design%20elegant%20professional%20photography&width=600&height=400&seq=${item}&orientation=landscape`}
                  alt={`مطبخ ${item}`}
                  className="w-full h-full object-cover hover:scale-110 transition-transform duration-700"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent opacity-0 hover:opacity-100 transition-opacity duration-500"></div>
                <div className="absolute top-4 left-4 bg-white/90 backdrop-blur-sm text-gray-800 px-3 py-1 rounded-full text-xs font-medium">
                  مطبخ عصري
                </div>
              </div>
              
              <div className="p-6">
                <h3 className="text-xl font-bold text-gray-800 mb-2">
                  مطبخ عصري {item}
                </h3>
                <p className="text-gray-600 text-sm mb-4 leading-relaxed">
                  تصميم أنيق وعصري يجمع بين الجمال والوظائف العملية
                </p>
                
                <div className="flex flex-wrap gap-2 mb-4">
                  <span className="px-3 py-1 bg-gray-100 text-gray-700 text-xs rounded-full font-medium">
                    تصميم حديث
                  </span>
                  <span className="px-3 py-1 bg-gray-100 text-gray-700 text-xs rounded-full font-medium">
                    جودة عالية
                  </span>
                </div>
                
                <button className="w-full bg-gradient-to-r from-orange-500 to-red-500 text-white py-3 rounded-xl font-medium hover:shadow-lg transition-all duration-300">
                  <div className="flex items-center justify-center space-x-2 rtl:space-x-reverse">
                    <i className="ri-eye-line"></i>
                    <span>عرض التفاصيل</span>
                  </div>
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default KitchenGallerySimple;
