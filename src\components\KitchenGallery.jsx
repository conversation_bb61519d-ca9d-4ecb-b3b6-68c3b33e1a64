import { useRef, useState } from 'react';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation, Pagination, EffectCoverflow, Autoplay, Thumbs } from 'swiper/modules';
// eslint-disable-next-line no-unused-vars
import { motion, useInView, AnimatePresence } from 'framer-motion';

// Import Swiper styles
import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';
import 'swiper/css/effect-coverflow';
import 'swiper/css/thumbs';

const KitchenGallery = () => {
  const sectionRef = useRef(null);
  const [thumbsSwiper, setThumbsSwiper] = useState(null);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [lightboxImage, setLightboxImage] = useState(null);
  const [modalImageIndex, setModalImageIndex] = useState(0);
  const isInView = useInView(sectionRef, { once: true, threshold: 0.1 });

  const categories = [
    { id: 'all', name: 'جميع التصاميم', icon: 'ri-apps-line' },
    { id: 'modern', name: 'عصري', icon: 'ri-building-line' },
    { id: 'classic', name: 'كلاسيكي', icon: 'ri-home-heart-line' },
    { id: 'luxury', name: 'فاخر', icon: 'ri-vip-crown-line' },
    { id: 'minimal', name: 'مينيمال', icon: 'ri-layout-line' }
  ];

  const kitchens = [
    {
      id: 1,
      images: [
        "https://readdy.ai/api/search-image?query=elegant%20modern%20kitchen%20with%20island%2C%20white%20cabinets%2C%20marble%20countertops%2C%20pendant%20lights%2C%20wooden%20accents%2C%20spacious%20design%2C%20professional%20photography%2C%20advertisement%20quality&width=600&height=800&seq=2&orientation=portrait",
        "https://readdy.ai/api/search-image?query=modern%20white%20kitchen%20island%20close%20up%2C%20marble%20countertop%20detail%2C%20pendant%20lighting%2C%20professional%20photography&width=800&height=600&seq=21&orientation=landscape",
        "https://readdy.ai/api/search-image?query=modern%20kitchen%20cabinets%20interior%20storage%2C%20organized%20shelves%2C%20white%20finish%2C%20professional%20photography&width=600&height=800&seq=22&orientation=portrait"
      ],
      title: "مطبخ مودرن أبيض",
      description: "تصميم عصري مع جزيرة مركزية ورخام فاخر يجمع بين الأناقة والوظائف العملية",
      category: 'modern',
      features: ['جزيرة مركزية', 'رخام طبيعي', 'إضاءة LED']
    },
    {
      id: 2,
      images: [
        "https://readdy.ai/api/search-image?query=luxury%20dark%20kitchen%20with%20gold%20accents%2C%20black%20cabinets%2C%20marble%20countertops%2C%20modern%20appliances%2C%20elegant%20lighting%2C%20professional%20photography%2C%20advertisement%20quality&width=800&height=600&seq=3&orientation=landscape",
        "https://readdy.ai/api/search-image?query=luxury%20kitchen%20gold%20hardware%20details%2C%20black%20cabinets%20close%20up%2C%20premium%20finishes%2C%20professional%20photography&width=600&height=800&seq=23&orientation=portrait",
        "https://readdy.ai/api/search-image?query=luxury%20kitchen%20appliances%20setup%2C%20modern%20stove%20and%20hood%2C%20dark%20elegant%20design%2C%20professional%20photography&width=800&height=600&seq=24&orientation=landscape"
      ],
      title: "مطبخ فاخر داكن",
      description: "تصميم أنيق مع لمسات ذهبية وأجهزة حديثة يعكس الفخامة والرقي",
      category: 'luxury',
      features: ['لمسات ذهبية', 'أجهزة ذكية', 'تشطيبات فاخرة']
    },
    {
      id: 3,
      images: [
        "https://readdy.ai/api/search-image?query=rustic%20kitchen%20with%20wooden%20cabinets%2C%20stone%20countertops%2C%20farmhouse%20sink%2C%20warm%20lighting%2C%20cozy%20atmosphere%2C%20professional%20photography%2C%20advertisement%20quality&width=600&height=800&seq=4&orientation=portrait",
        "https://readdy.ai/api/search-image?query=rustic%20kitchen%20wooden%20details%2C%20natural%20wood%20grain%2C%20stone%20backsplash%2C%20warm%20atmosphere%2C%20professional%20photography&width=800&height=600&seq=25&orientation=landscape",
        "https://readdy.ai/api/search-image?query=farmhouse%20kitchen%20sink%20area%2C%20wooden%20cabinets%2C%20stone%20countertop%2C%20rustic%20charm%2C%20professional%20photography&width=600&height=800&seq=26&orientation=portrait"
      ],
      title: "مطبخ ريفي خشبي",
      description: "تصميم دافئ مع أسطح حجرية وإضاءة هادئة يخلق جواً مريحاً ومألوفاً",
      category: 'classic',
      features: ['خشب طبيعي', 'أسطح حجرية', 'طابع ريفي']
    },
    {
      id: 4,
      images: [
        "https://readdy.ai/api/search-image?query=minimalist%20kitchen%20with%20clean%20lines%2C%20white%20and%20wood%20combination%2C%20hidden%20storage%2C%20sleek%20appliances%2C%20natural%20light%2C%20professional%20photography%2C%20advertisement%20quality&width=800&height=600&seq=5&orientation=landscape",
        "https://readdy.ai/api/search-image?query=minimalist%20kitchen%20hidden%20storage%20solutions%2C%20clean%20white%20cabinets%2C%20organized%20interior%2C%20professional%20photography&width=600&height=800&seq=27&orientation=portrait",
        "https://readdy.ai/api/search-image?query=minimalist%20kitchen%20appliances%20integration%2C%20sleek%20design%2C%20white%20and%20wood%2C%20professional%20photography&width=800&height=600&seq=28&orientation=landscape"
      ],
      title: "مطبخ مينيمال",
      description: "تصميم بسيط وأنيق مع تخزين مخفي وخطوط نظيفة يركز على الوظائف الأساسية",
      category: 'minimal',
      features: ['تخزين مخفي', 'خطوط نظيفة', 'تصميم بسيط']
    },
    {
      id: 5,
      images: [
        "https://readdy.ai/api/search-image?query=contemporary%20kitchen%20with%20blue%20cabinets%2C%20quartz%20countertops%2C%20open%20shelving%2C%20modern%20lighting%2C%20stylish%20backsplash%2C%20professional%20photography%2C%20advertisement%20quality&width=600&height=800&seq=6&orientation=portrait",
        "https://readdy.ai/api/search-image?query=contemporary%20blue%20kitchen%20open%20shelving%20detail%2C%20modern%20styling%2C%20quartz%20countertop%2C%20professional%20photography&width=800&height=600&seq=29&orientation=landscape",
        "https://readdy.ai/api/search-image?query=contemporary%20kitchen%20backsplash%20design%2C%20blue%20cabinets%2C%20modern%20lighting%20fixtures%2C%20professional%20photography&width=600&height=800&seq=30&orientation=portrait"
      ],
      title: "مطبخ معاصر أزرق",
      description: "تصميم عصري مع أرفف مفتوحة وإضاءة حديثة يضفي لمسة من الحيوية والعصرية",
      category: 'modern',
      features: ['أرفف مفتوحة', 'إضاءة حديثة', 'ألوان جريئة']
    },
    {
      id: 6,
      images: [
        "https://readdy.ai/api/search-image?query=industrial%20style%20kitchen%20with%20concrete%20countertops%2C%20metal%20accents%2C%20exposed%20brick%2C%20pendant%20lights%2C%20professional%20appliances%2C%20professional%20photography%2C%20advertisement%20quality&width=800&height=600&seq=7&orientation=landscape",
        "https://readdy.ai/api/search-image?query=industrial%20kitchen%20concrete%20countertop%20detail%2C%20metal%20fixtures%2C%20exposed%20elements%2C%20professional%20photography&width=600&height=800&seq=31&orientation=portrait",
        "https://readdy.ai/api/search-image?query=industrial%20kitchen%20exposed%20brick%20wall%2C%20pendant%20lighting%2C%20metal%20accents%2C%20professional%20photography&width=800&height=600&seq=32&orientation=landscape"
      ],
      title: "مطبخ بطابع صناعي",
      description: "تصميم عصري مع أسطح خرسانية ولمسات معدنية يجمع بين القوة والأناقة الحديثة",
      category: 'modern',
      features: ['طابع صناعي', 'أسطح خرسانية', 'لمسات معدنية']
    }
  ];

  const filteredKitchens = selectedCategory === 'all'
    ? kitchens
    : kitchens.filter(kitchen => kitchen.category === selectedCategory);

  return (
    <section id="kitchens" className="relative py-24 bg-gradient-to-br from-slate-50 via-white to-blue-50 overflow-hidden" ref={sectionRef}>
      {/* Background Decorations */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-blue-200/20 to-purple-200/20 rounded-full blur-3xl"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-br from-pink-200/20 to-orange-200/20 rounded-full blur-3xl"></div>
      </div>

      <div className="container mx-auto px-6 relative z-10">
        {/* Enhanced Header */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 50 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
          transition={{ duration: 0.8 }}
        >
          <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-orange-500 to-red-500 rounded-2xl mb-6 shadow-lg">
            <i className="ri-restaurant-line text-2xl text-white"></i>
          </div>
          <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold bg-gradient-to-r from-gray-800 via-gray-900 to-gray-800 bg-clip-text text-transparent mb-6">
            معرض
            <span className="block bg-gradient-to-r from-orange-600 via-red-600 to-pink-600 bg-clip-text text-transparent">
              المطابخ الفاخرة
            </span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            استكشف مجموعتنا المتنوعة من تصاميم المطابخ العصرية والكلاسيكية التي تناسب مختلف الأذواق والمساحات
          </p>
          <div className="w-24 h-1 bg-gradient-to-r from-orange-500 to-red-500 mx-auto mt-8 rounded-full"></div>
        </motion.div>

        {/* Category Filter */}
        <motion.div
          className="flex flex-wrap justify-center gap-4 mb-12"
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.8, delay: 0.2 }}
        >
          {categories.map((category, index) => (
            <motion.button
              key={category.id}
              onClick={() => setSelectedCategory(category.id)}
              className={`group relative px-6 py-3 rounded-2xl font-medium transition-all duration-300 ${
                selectedCategory === category.id
                  ? 'bg-gradient-to-r from-orange-500 to-red-500 text-white shadow-lg scale-105'
                  : 'bg-white/80 backdrop-blur-sm text-gray-700 hover:bg-white hover:scale-105 border border-gray-200'
              }`}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              initial={{ opacity: 0, y: 20 }}
              animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
            >
              <div className="flex items-center space-x-2 rtl:space-x-reverse">
                <i className={`${category.icon} text-lg`}></i>
                <span>{category.name}</span>
              </div>
              {selectedCategory !== category.id && (
                <div className="absolute inset-0 bg-gradient-to-r from-orange-500 to-red-500 rounded-2xl opacity-0 group-hover:opacity-10 transition-opacity duration-300"></div>
              )}
            </motion.button>
          ))}
        </motion.div>

        {/* Main Swiper Gallery */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          className="mb-8 max-w-7xl mx-auto"
        >
          <Swiper
            modules={[Navigation, Pagination, EffectCoverflow, Autoplay, Thumbs]}
            spaceBetween={30}
            slidesPerView={1}
            centeredSlides={false}
            effect="slide"
            autoplay={{
              delay: 4000,
              disableOnInteraction: false,
              pauseOnMouseEnter: true,
            }}
            navigation={{
              nextEl: '.swiper-button-next-custom',
              prevEl: '.swiper-button-prev-custom',
            }}
            pagination={{
              clickable: true,
              dynamicBullets: true,
            }}
            thumbs={{ swiper: thumbsSwiper }}
            loop={filteredKitchens.length > 2}
            breakpoints={{
              640: {
                slidesPerView: 1,
                spaceBetween: 20,
              },
              768: {
                slidesPerView: 2,
                spaceBetween: 30,
              },
              1024: {
                slidesPerView: 2,
                spaceBetween: 40,
              },
              1280: {
                slidesPerView: 2,
                spaceBetween: 50,
              },
            }}
            className="kitchen-swiper"
            style={{
              '--swiper-pagination-color': '#f97316',
              '--swiper-pagination-bullet-inactive-color': '#d1d5db',
              '--swiper-pagination-bullet-size': '12px',
              '--swiper-pagination-bullet-horizontal-gap': '6px'
            }}
          >
            <AnimatePresence mode="wait">
              {filteredKitchens.map((kitchen, index) => (
                <SwiperSlide key={kitchen.id}>
                  <motion.div
                    className="group relative bg-white rounded-3xl overflow-hidden shadow-xl hover:shadow-2xl transition-all duration-500 cursor-pointer"
                    whileHover={{ y: -10 }}
                    onClick={() => setLightboxImage(kitchen)}
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    exit={{ opacity: 0, scale: 0.8 }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                  >
                    {/* Image Container */}
                    <div className="relative h-80 md:h-96 overflow-hidden">
                      <motion.img
                        src={kitchen.images[0]}
                        alt={kitchen.title}
                        className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700"
                        whileHover={{ scale: 1.1 }}
                      />

                      {/* Gradient Overlay */}
                      <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>



                      {/* Category Badge */}
                      <div className="absolute top-4 left-4 bg-white/90 backdrop-blur-sm text-gray-800 px-3 py-1 rounded-full text-xs font-medium">
                        {categories.find(cat => cat.id === kitchen.category)?.name}
                      </div>

                      {/* Zoom Icon */}
                      <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                        <div className="w-16 h-16 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center border border-white/30">
                          <i className="ri-zoom-in-line text-2xl text-white"></i>
                        </div>
                      </div>
                    </div>

                    {/* Content */}
                    <div className="p-6">
                      <h3 className="text-xl font-bold text-gray-800 mb-2 group-hover:text-orange-600 transition-colors duration-300">
                        {kitchen.title}
                      </h3>
                      <p className="text-gray-600 text-sm mb-4 leading-relaxed">
                        {kitchen.description}
                      </p>

                      {/* Features */}
                      <div className="flex flex-wrap gap-2 mb-4">
                        {kitchen.features.map((feature, idx) => (
                          <span
                            key={idx}
                            className="px-3 py-1 bg-gray-100 text-gray-700 text-xs rounded-full font-medium"
                          >
                            {feature}
                          </span>
                        ))}
                      </div>

                      {/* Action Button */}
                      <motion.button
                        className="w-full bg-gradient-to-r from-orange-500 to-red-500 text-white py-3 rounded-xl font-medium hover:shadow-lg transition-all duration-300"
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        <div className="flex items-center justify-center space-x-2 rtl:space-x-reverse">
                          <i className="ri-eye-line"></i>
                          <span>عرض التفاصيل</span>
                        </div>
                      </motion.button>
                    </div>
                  </motion.div>
                </SwiperSlide>
              ))}
            </AnimatePresence>
          </Swiper>

          {/* Custom Navigation Buttons */}
          <div className="flex justify-center items-center space-x-4 rtl:space-x-reverse mt-8">
            <button className="swiper-button-prev-custom w-12 h-12 bg-white/80 backdrop-blur-sm rounded-full flex items-center justify-center shadow-lg hover:bg-white hover:scale-110 transition-all duration-300 border border-gray-200">
              <i className="ri-arrow-right-line text-xl text-gray-700"></i>
            </button>
            <button className="swiper-button-next-custom w-12 h-12 bg-white/80 backdrop-blur-sm rounded-full flex items-center justify-center shadow-lg hover:bg-white hover:scale-110 transition-all duration-300 border border-gray-200">
              <i className="ri-arrow-left-line text-xl text-gray-700"></i>
            </button>
          </div>
        </motion.div>

        {/* Thumbnails Swiper */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          className="mb-12"
        >
          <Swiper
            onSwiper={setThumbsSwiper}
            spaceBetween={10}
            slidesPerView={3}
            freeMode={true}
            watchSlidesProgress={true}
            centeredSlides={true}
            breakpoints={{
              640: {
                slidesPerView: 4,
                spaceBetween: 15,
              },
              768: {
                slidesPerView: 5,
                spaceBetween: 20,
              },
              1024: {
                slidesPerView: 6,
                spaceBetween: 20,
              },
              1280: {
                slidesPerView: 7,
                spaceBetween: 25,
              },
            }}
            className="thumbs-swiper"
          >
            {filteredKitchens.map((kitchen) => (
              <SwiperSlide key={`thumb-${kitchen.id}`}>
                <div className="relative h-20 md:h-24 rounded-xl overflow-hidden cursor-pointer group border-2 border-transparent hover:border-orange-500 transition-all duration-300">
                  <img
                    src={kitchen.images[0]}
                    alt={kitchen.title}
                    className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
                  />
                  <div className="absolute inset-0 bg-black/20 group-hover:bg-black/0 transition-colors duration-300"></div>
                </div>
              </SwiperSlide>
            ))}
          </Swiper>
        </motion.div>

        {/* Stats Section */}
  

        {/* CTA Button */}
        <motion.div
          className="text-center"
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.8, delay: 1 }}
        >
        </motion.div>
      </div>

      {/* Enhanced Modal */}
      <AnimatePresence>
        {lightboxImage && (
          <motion.div
            className="fixed inset-0 bg-black/90 backdrop-blur-sm z-50 flex items-center justify-center p-4"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={() => {
              setLightboxImage(null);
              setModalImageIndex(0);
            }}
          >
            {/* Mobile Modal */}
            <motion.div
              className="relative w-full max-w-4xl h-[90vh] bg-white rounded-3xl overflow-hidden shadow-2xl flex flex-col lg:hidden"
              initial={{ scale: 0.5, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.5, opacity: 0 }}
              transition={{ type: "spring", damping: 25, stiffness: 300 }}
              onClick={(e) => e.stopPropagation()}
            >
              {/* Close Button */}
              <button
                onClick={() => {
                  setLightboxImage(null);
                  setModalImageIndex(0);
                }}
                className="absolute top-4 right-4 z-10 w-12 h-12 bg-black/50 backdrop-blur-sm rounded-full flex items-center justify-center text-white hover:bg-black/70 transition-colors duration-300"
              >
                <i className="ri-close-line text-xl"></i>
              </button>

              {/* Image Slider */}
              <div className="relative flex-1">
                <Swiper
                  modules={[Navigation, Pagination]}
                  spaceBetween={0}
                  slidesPerView={1}
                  navigation={{
                    nextEl: '.modal-swiper-button-next',
                    prevEl: '.modal-swiper-button-prev',
                  }}
                  pagination={{
                    clickable: true,
                    dynamicBullets: true,
                  }}
                  onSlideChange={(swiper) => setModalImageIndex(swiper.activeIndex)}
                  className="modal-swiper h-full"
                  style={{
                    '--swiper-pagination-color': '#f97316',
                    '--swiper-pagination-bullet-inactive-color': '#d1d5db',
                  }}
                >
                  {lightboxImage.images.map((image, index) => (
                    <SwiperSlide key={index}>
                      <div className="relative h-full">
                        <img
                          src={image}
                          alt={`${lightboxImage.title} - صورة ${index + 1}`}
                          className="w-full h-full object-cover"
                        />
                      </div>
                    </SwiperSlide>
                  ))}
                </Swiper>

                {/* Custom Navigation Buttons */}
                {lightboxImage.images.length > 1 && (
                  <>
                    <button className="modal-swiper-button-prev absolute left-4 top-1/2 -translate-y-1/2 z-10 w-12 h-12 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center text-white hover:bg-white/30 transition-all duration-300">
                      <i className="ri-arrow-right-line text-xl"></i>
                    </button>
                    <button className="modal-swiper-button-next absolute right-4 top-1/2 -translate-y-1/2 z-10 w-12 h-12 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center text-white hover:bg-white/30 transition-all duration-300">
                      <i className="ri-arrow-left-line text-xl"></i>
                    </button>
                  </>
                )}

                {/* Image Counter */}
                {lightboxImage.images.length > 1 && (
                  <div className="absolute bottom-4 left-4 bg-black/50 backdrop-blur-sm text-white px-3 py-1 rounded-full text-sm">
                    {modalImageIndex + 1} / {lightboxImage.images.length}
                  </div>
                )}
              </div>

              {/* Content Section */}
              <div className="p-6 bg-gradient-to-r from-gray-50 to-white">
                <div className="text-center mb-6">
                  <h3 className="text-2xl font-bold text-gray-800 mb-4">
                    {lightboxImage.title}
                  </h3>
                  <p className="text-gray-600 leading-relaxed text-base">
                    {lightboxImage.description}
                  </p>
                </div>

                {/* Contact Section */}
                <div className="text-center">
                  <h4 className="text-xl font-bold bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent mb-6">
                    اطلب الآن
                  </h4>

                  {/* Social Media Icons */}
                  <div className="flex justify-center items-center gap-4 flex-wrap">
                    {/* WhatsApp */}
                    <motion.a
                      href="https://wa.me/966500000000"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="group flex items-center justify-center w-12 h-12 bg-gradient-to-r from-green-500 to-green-600 rounded-full text-white shadow-lg hover:shadow-xl transition-all duration-300"
                      whileHover={{ scale: 1.1, y: -2 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <i className="ri-whatsapp-line text-xl"></i>
                    </motion.a>

                    {/* Instagram */}
                    <motion.a
                      href="https://instagram.com/expertwonders"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="group flex items-center justify-center w-12 h-12 bg-gradient-to-r from-pink-500 to-purple-600 rounded-full text-white shadow-lg hover:shadow-xl transition-all duration-300"
                      whileHover={{ scale: 1.1, y: -2 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <i className="ri-instagram-line text-xl"></i>
                    </motion.a>

                    {/* Twitter */}
                    <motion.a
                      href="https://twitter.com/expertwonders"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="group flex items-center justify-center w-12 h-12 bg-gradient-to-r from-blue-400 to-blue-600 rounded-full text-white shadow-lg hover:shadow-xl transition-all duration-300"
                      whileHover={{ scale: 1.1, y: -2 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <i className="ri-twitter-line text-xl"></i>
                    </motion.a>

                    {/* Snapchat */}
                    <motion.a
                      href="https://snapchat.com/add/expertwonders"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="group flex items-center justify-center w-12 h-12 bg-gradient-to-r from-yellow-400 to-yellow-500 rounded-full text-white shadow-lg hover:shadow-xl transition-all duration-300"
                      whileHover={{ scale: 1.1, y: -2 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <i className="ri-snapchat-line text-lg"></i>
                    </motion.a>

                    {/* TikTok */}
                    <motion.a
                      href="https://tiktok.com/@expertwonders"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="group flex items-center justify-center w-12 h-12 bg-gradient-to-r from-gray-800 to-black rounded-full text-white shadow-lg hover:shadow-xl transition-all duration-300"
                      whileHover={{ scale: 1.1, y: -2 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <i className="ri-tiktok-line text-xl"></i>
                    </motion.a>
                  </div>

                  {/* Contact Text */}
                  <p className="text-gray-500 text-sm mt-4">
                    تواصل معنا عبر أي من منصات التواصل الاجتماعي للحصول على استشارة مجانية
                  </p>
                </div>
              </div>
            </motion.div>

            {/* Desktop Modal */}
            <motion.div
              className="relative w-full max-w-5xl h-[80vh] bg-white rounded-3xl overflow-hidden shadow-2xl hidden lg:grid lg:grid-cols-3"
              initial={{ scale: 0.5, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.5, opacity: 0 }}
              transition={{ type: "spring", damping: 25, stiffness: 300 }}
              onClick={(e) => e.stopPropagation()}
            >
              {/* Close Button */}
              <button
                onClick={() => {
                  setLightboxImage(null);
                  setModalImageIndex(0);
                }}
                className="absolute top-4 right-4 z-10 w-10 h-10 bg-black/50 backdrop-blur-sm rounded-full flex items-center justify-center text-white hover:bg-black/70 transition-colors duration-300"
              >
                <i className="ri-close-line text-lg"></i>
              </button>

              {/* Left Column - Main Image */}
              <div className="col-span-2 relative">
                <div className="h-full relative">
                  <img
                    src={lightboxImage.images[modalImageIndex]}
                    alt={`${lightboxImage.title} - صورة ${modalImageIndex + 1}`}
                    className="w-full h-full object-cover"
                  />

                  {/* Navigation Buttons */}
                  {lightboxImage.images.length > 1 && (
                    <>
                      <button
                        onClick={() => setModalImageIndex(modalImageIndex > 0 ? modalImageIndex - 1 : lightboxImage.images.length - 1)}
                        className="absolute left-4 top-1/2 -translate-y-1/2 w-10 h-10 bg-black/50 backdrop-blur-sm rounded-full flex items-center justify-center text-white hover:bg-black/70 transition-all duration-300"
                      >
                        <i className="ri-arrow-right-line text-lg"></i>
                      </button>
                      <button
                        onClick={() => setModalImageIndex(modalImageIndex < lightboxImage.images.length - 1 ? modalImageIndex + 1 : 0)}
                        className="absolute right-4 top-1/2 -translate-y-1/2 w-10 h-10 bg-black/50 backdrop-blur-sm rounded-full flex items-center justify-center text-white hover:bg-black/70 transition-all duration-300"
                      >
                        <i className="ri-arrow-left-line text-lg"></i>
                      </button>
                    </>
                  )}

                  {/* Image Counter */}
                  {lightboxImage.images.length > 1 && (
                    <div className="absolute bottom-4 left-4 bg-black/50 backdrop-blur-sm text-white px-3 py-1 rounded-full text-sm">
                      {modalImageIndex + 1} / {lightboxImage.images.length}
                    </div>
                  )}
                </div>
              </div>

              {/* Right Column - Content */}
              <div className="bg-gradient-to-b from-gray-50 to-white p-6 flex flex-col justify-between">
                {/* Top Section - Title and Description */}
                <div>
                  <h3 className="text-2xl font-bold text-gray-800 mb-4">
                    {lightboxImage.title}
                  </h3>
                  <p className="text-gray-600 leading-relaxed text-sm mb-6">
                    {lightboxImage.description}
                  </p>

                  {/* Thumbnail Images */}
                  {lightboxImage.images.length > 1 && (
                    <div className="mb-6">
                      <div className="grid grid-cols-3 gap-2">
                        {lightboxImage.images.map((image, index) => (
                          <button
                            key={index}
                            onClick={() => setModalImageIndex(index)}
                            className={`relative h-16 rounded-lg overflow-hidden border-2 transition-all duration-300 ${
                              modalImageIndex === index
                                ? 'border-orange-500 scale-105'
                                : 'border-gray-200 hover:border-orange-300'
                            }`}
                          >
                            <img
                              src={image}
                              alt={`${lightboxImage.title} - صورة ${index + 1}`}
                              className="w-full h-full object-cover"
                            />
                          </button>
                        ))}
                      </div>
                    </div>
                  )}
                </div>

                {/* Bottom Section - Contact */}
                <div>
                  <h4 className="text-lg font-bold bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent mb-4 text-center">
                    اطلب الآن
                  </h4>

                  {/* Social Media Icons */}
                  <div className="grid grid-cols-3 gap-3 mb-4">
                    {/* WhatsApp */}
                    <motion.a
                      href="https://wa.me/966500000000"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center justify-center h-12 bg-gradient-to-r from-green-500 to-green-600 rounded-xl text-white shadow-lg hover:shadow-xl transition-all duration-300"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <i className="ri-whatsapp-line text-lg"></i>
                    </motion.a>

                    {/* Instagram */}
                    <motion.a
                      href="https://instagram.com/expertwonders"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center justify-center h-12 bg-gradient-to-r from-pink-500 to-purple-600 rounded-xl text-white shadow-lg hover:shadow-xl transition-all duration-300"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <i className="ri-instagram-line text-lg"></i>
                    </motion.a>

                    {/* Twitter */}
                    <motion.a
                      href="https://twitter.com/expertwonders"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center justify-center h-12 bg-gradient-to-r from-blue-400 to-blue-600 rounded-xl text-white shadow-lg hover:shadow-xl transition-all duration-300"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <i className="ri-twitter-line text-lg"></i>
                    </motion.a>

                    {/* Snapchat */}
                    <motion.a
                      href="https://snapchat.com/add/expertwonders"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center justify-center h-12 bg-gradient-to-r from-yellow-400 to-yellow-500 rounded-xl text-white shadow-lg hover:shadow-xl transition-all duration-300"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <i className="ri-snapchat-line text-lg"></i>
                    </motion.a>

                    {/* TikTok */}
                    <motion.a
                      href="https://tiktok.com/@expertwonders"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center justify-center h-12 bg-gradient-to-r from-gray-800 to-black rounded-xl text-white shadow-lg hover:shadow-xl transition-all duration-300"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <i className="ri-tiktok-line text-lg"></i>
                    </motion.a>

                    {/* Phone */}
                    <motion.a
                      href="tel:+966500000000"
                      className="flex items-center justify-center h-12 bg-gradient-to-r from-emerald-500 to-teal-600 rounded-xl text-white shadow-lg hover:shadow-xl transition-all duration-300"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <i className="ri-phone-line text-lg"></i>
                    </motion.a>
                  </div>

                  {/* Contact Text */}
                  <p className="text-gray-500 text-xs text-center">
                    تواصل معنا للحصول على استشارة مجانية
                  </p>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </section>
  );
};

export default KitchenGallery;
