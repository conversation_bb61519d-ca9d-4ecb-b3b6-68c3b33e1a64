import React from 'react';

const CabinetGallerySimple = () => {
  return (
    <section id="cabinets" className="py-24 bg-gradient-to-br from-slate-50 via-white to-purple-50">
      <div className="container mx-auto px-6">
        <div className="text-center mb-16">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-purple-500 to-blue-500 rounded-2xl mb-6 shadow-lg">
            <i className="ri-archive-line text-2xl text-white"></i>
          </div>
          <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold bg-gradient-to-r from-gray-800 via-gray-900 to-gray-800 bg-clip-text text-transparent mb-6">
            معرض
            <span className="block bg-gradient-to-r from-purple-600 via-blue-600 to-indigo-600 bg-clip-text text-transparent">
              الخزائن الأنيقة
            </span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            تصاميم خزائن عصرية وأنيقة تجمع بين الجمال والوظيفة لتلبية احتياجات التخزين مع الحفاظ على جمالية المكان
          </p>
          <div className="w-24 h-1 bg-gradient-to-r from-purple-500 to-blue-500 mx-auto mt-8 rounded-full"></div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {[1, 2, 3, 4, 5, 6].map((item) => (
            <div key={item} className="bg-white rounded-3xl overflow-hidden shadow-xl hover:shadow-2xl transition-all duration-500">
              <div className="relative h-64 overflow-hidden">
                <img
                  src={`https://readdy.ai/api/search-image?query=modern%20cabinet%20wardrobe%20design%20elegant%20professional%20photography&width=600&height=400&seq=${item}&orientation=landscape`}
                  alt={`خزانة ${item}`}
                  className="w-full h-full object-cover hover:scale-110 transition-transform duration-700"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent opacity-0 hover:opacity-100 transition-opacity duration-500"></div>
                <div className="absolute top-4 left-4 bg-white/90 backdrop-blur-sm text-gray-800 px-3 py-1 rounded-full text-xs font-medium">
                  خزانة عصرية
                </div>
              </div>
              
              <div className="p-6">
                <h3 className="text-xl font-bold text-gray-800 mb-2">
                  خزانة أنيقة {item}
                </h3>
                <p className="text-gray-600 text-sm mb-4 leading-relaxed">
                  تصميم عصري وأنيق يوفر حلول تخزين مثالية مع لمسة جمالية
                </p>
                
                <div className="flex flex-wrap gap-2 mb-4">
                  <span className="px-3 py-1 bg-gray-100 text-gray-700 text-xs rounded-full font-medium">
                    تصميم حديث
                  </span>
                  <span className="px-3 py-1 bg-gray-100 text-gray-700 text-xs rounded-full font-medium">
                    جودة عالية
                  </span>
                </div>
                
                <button className="w-full bg-gradient-to-r from-purple-500 to-blue-500 text-white py-3 rounded-xl font-medium hover:shadow-lg transition-all duration-300">
                  <div className="flex items-center justify-center space-x-2 rtl:space-x-reverse">
                    <i className="ri-eye-line"></i>
                    <span>عرض التفاصيل</span>
                  </div>
                </button>
              </div>
            </div>
          ))}
        </div>

        {/* Stats Section */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mt-16">
          {[
            { number: '300+', label: 'خزانة منجزة', icon: 'ri-archive-line', color: 'from-purple-500 to-indigo-500' },
            { number: '12+', label: 'سنة خبرة', icon: 'ri-award-line', color: 'from-blue-500 to-cyan-500' },
            { number: '95%', label: 'رضا العملاء', icon: 'ri-heart-line', color: 'from-pink-500 to-rose-500' },
            { number: '24/7', label: 'دعم فني', icon: 'ri-customer-service-line', color: 'from-green-500 to-emerald-500' }
          ].map((stat, index) => (
            <div
              key={index}
              className="text-center p-6 bg-white/80 backdrop-blur-sm rounded-2xl border border-white/50 hover:bg-white hover:scale-105 transition-all duration-300 shadow-lg"
            >
              <div className={`w-16 h-16 mx-auto mb-4 bg-gradient-to-r ${stat.color} rounded-2xl flex items-center justify-center shadow-lg`}>
                <i className={`${stat.icon} text-2xl text-white`}></i>
              </div>
              <div className={`text-3xl font-bold bg-gradient-to-r ${stat.color} bg-clip-text text-transparent mb-2`}>
                {stat.number}
              </div>
              <p className="text-gray-600 font-medium">{stat.label}</p>
            </div>
          ))}
        </div>

        {/* CTA Button */}
        <div className="text-center mt-12">
          <button className="bg-gradient-to-r from-purple-500 to-blue-500 hover:from-purple-600 hover:to-blue-600 text-white px-12 py-4 rounded-2xl font-bold text-lg shadow-xl hover:shadow-2xl transition-all duration-300">
            <div className="flex items-center space-x-3 rtl:space-x-reverse">
              <i className="ri-phone-line text-xl"></i>
              <span>احجز استشارة مجانية</span>
              <i className="ri-arrow-left-line text-xl"></i>
            </div>
          </button>
        </div>
      </div>
    </section>
  );
};

export default CabinetGallerySimple;
