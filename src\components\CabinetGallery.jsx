import { useRef, useState } from 'react';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation, Pagination, Autoplay, Thumbs } from 'swiper/modules';
import { useInView, AnimatePresence } from 'framer-motion';

// Import Swiper styles
import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';
import 'swiper/css/thumbs';

const CabinetGallery = () => {
  const sectionRef = useRef(null);
  const [thumbsSwiper, setThumbsSwiper] = useState(null);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [lightboxImage, setLightboxImage] = useState(null);
  const [modalImageIndex, setModalImageIndex] = useState(0);
  const isInView = useInView(sectionRef, { once: true, threshold: 0.1 });

  const categories = [
    { id: 'all', name: 'جميع التصاميم', icon: 'ri-apps-line' },
    { id: 'wardrobe', name: 'خزائن ملابس', icon: 'ri-shirt-line' },
    { id: 'kitchen', name: 'خزائن مطبخ', icon: 'ri-restaurant-line' },
    { id: 'office', name: 'خزائن مكتبية', icon: 'ri-briefcase-line' },
    { id: 'display', name: 'خزائن عرض', icon: 'ri-tv-line' }
  ];

  const cabinets = [
    {
      id: 1,
      images: [
        "https://readdy.ai/api/search-image?query=modern%20wardrobe%20with%20sliding%20doors%2C%20elegant%20design%2C%20built-in%20lighting%2C%20organized%20interior%2C%20neutral%20colors%2C%20professional%20photography%2C%20advertisement%20quality&width=600&height=800&seq=8&orientation=portrait",
        "https://readdy.ai/api/search-image?query=modern%20wardrobe%20interior%20organization%2C%20sliding%20doors%20open%2C%20built-in%20lighting%2C%20shelves%20and%20drawers%2C%20professional%20photography&width=800&height=600&seq=33&orientation=landscape",
        "https://readdy.ai/api/search-image?query=modern%20wardrobe%20sliding%20door%20mechanism%2C%20elegant%20handles%2C%20smooth%20operation%2C%20professional%20photography&width=600&height=800&seq=34&orientation=portrait"
      ],
      title: "خزانة ملابس عصرية",
      description: "تصميم أنيق مع أبواب منزلقة وإضاءة داخلية يوفر تنظيماً مثالياً للملابس",
      category: 'wardrobe',
      features: ['أبواب منزلقة', 'إضاءة LED', 'تنظيم داخلي']
    },
    {
      id: 2,
      images: [
        "https://readdy.ai/api/search-image?query=luxury%20walk-in%20closet%2C%20custom%20cabinetry%2C%20island%20storage%2C%20glass%20display%20cases%2C%20elegant%20lighting%2C%20organized%20shelving%2C%20professional%20photography%2C%20advertisement%20quality&width=800&height=600&seq=9&orientation=landscape",
        "https://readdy.ai/api/search-image?query=luxury%20walk-in%20closet%20island%20detail%2C%20custom%20drawers%2C%20jewelry%20storage%2C%20elegant%20design%2C%20professional%20photography&width=600&height=800&seq=35&orientation=portrait",
        "https://readdy.ai/api/search-image?query=luxury%20closet%20glass%20display%20cases%2C%20shoe%20storage%2C%20elegant%20lighting%2C%20organized%20system%2C%20professional%20photography&width=800&height=600&seq=36&orientation=landscape"
      ],
      title: "غرفة ملابس فاخرة",
      description: "تصميم مخصص مع جزيرة تخزين وعرض زجاجي يجمع بين الفخامة والوظائف العملية",
      category: 'wardrobe',
      features: ['جزيرة تخزين', 'عرض زجاجي', 'إضاءة فاخرة']
    },
    {
      id: 3,
      images: [
        "https://readdy.ai/api/search-image?query=minimalist%20bookshelf%20design%2C%20clean%20lines%2C%20wooden%20shelves%2C%20modern%20storage%20solution%2C%20elegant%20display%2C%20professional%20photography%2C%20advertisement%20quality&width=600&height=800&seq=10&orientation=portrait",
        "https://readdy.ai/api/search-image?query=minimalist%20bookshelf%20with%20books%20arranged%2C%20clean%20wooden%20shelves%2C%20modern%20display%2C%20professional%20photography&width=800&height=600&seq=37&orientation=landscape",
        "https://readdy.ai/api/search-image?query=minimalist%20bookshelf%20corner%20detail%2C%20wooden%20finish%2C%20clean%20lines%2C%20modern%20design%2C%20professional%20photography&width=600&height=800&seq=38&orientation=portrait"
      ],
      title: "مكتبة بتصميم مينيمال",
      description: "رفوف خشبية أنيقة بخطوط بسيطة ونظيفة تضفي لمسة عصرية على المكان",
      category: 'display',
      features: ['خشب طبيعي', 'تصميم مينيمال', 'رفوف قابلة للتعديل']
    },
    {
      id: 4,
      images: [
        "https://readdy.ai/api/search-image?query=built-in%20wall%20cabinet%20with%20hidden%20storage%2C%20custom%20design%2C%20modern%20finish%2C%20integrated%20with%20wall%2C%20elegant%20solution%2C%20professional%20photography%2C%20advertisement%20quality&width=800&height=600&seq=11&orientation=landscape",
        "https://readdy.ai/api/search-image?query=built-in%20wall%20cabinet%20interior%2C%20hidden%20storage%20compartments%2C%20organized%20shelves%2C%20professional%20photography&width=600&height=800&seq=39&orientation=portrait",
        "https://readdy.ai/api/search-image?query=wall%20cabinet%20integration%20detail%2C%20seamless%20design%2C%20modern%20finish%2C%20custom%20fit%2C%20professional%20photography&width=800&height=600&seq=40&orientation=landscape"
      ],
      title: "خزانة جدارية مدمجة",
      description: "تصميم مخصص مع تخزين مخفي ومتكامل مع الجدار لاستغلال أمثل للمساحة",
      category: 'kitchen',
      features: ['تصميم مدمج', 'تخزين مخفي', 'استغلال المساحة']
    },
    {
      id: 5,
      images: [
        "https://readdy.ai/api/search-image?query=luxury%20TV%20cabinet%20with%20display%20shelves%2C%20elegant%20design%2C%20integrated%20lighting%2C%20media%20storage%2C%20modern%20finish%2C%20professional%20photography%2C%20advertisement%20quality&width=600&height=800&seq=12&orientation=portrait",
        "https://readdy.ai/api/search-image?query=luxury%20TV%20cabinet%20with%20media%20equipment%2C%20cable%20management%2C%20storage%20compartments%2C%20professional%20photography&width=800&height=600&seq=41&orientation=landscape",
        "https://readdy.ai/api/search-image?query=TV%20cabinet%20lighting%20detail%2C%20integrated%20LED%20strips%2C%20elegant%20display%2C%20modern%20design%2C%20professional%20photography&width=600&height=800&seq=42&orientation=portrait"
      ],
      title: "وحدة تلفزيون فاخرة",
      description: "تصميم أنيق مع رفوف عرض وإضاءة متكاملة يجمع بين الجمال والوظائف العملية",
      category: 'display',
      features: ['رفوف عرض', 'إضاءة متكاملة', 'إدارة الكابلات']
    },
    {
      id: 6,
      images: [
        "https://readdy.ai/api/search-image?query=custom%20office%20cabinets%20and%20shelving%2C%20workspace%20storage%20solution%2C%20elegant%20design%2C%20organized%20system%2C%20professional%20look%2C%20advertisement%20quality&width=800&height=600&seq=13&orientation=landscape",
        "https://readdy.ai/api/search-image?query=office%20cabinet%20interior%20organization%2C%20file%20storage%2C%20drawer%20systems%2C%20workspace%20efficiency%2C%20professional%20photography&width=600&height=800&seq=43&orientation=portrait",
        "https://readdy.ai/api/search-image?query=office%20cabinet%20desk%20integration%2C%20workspace%20design%2C%20storage%20solutions%2C%20professional%20environment%2C%20professional%20photography&width=800&height=600&seq=44&orientation=landscape"
      ],
      title: "خزائن مكتبية مخصصة",
      description: "حلول تخزين أنيقة لمساحات العمل المكتبية تعزز الإنتاجية والتنظيم",
      category: 'office',
      features: ['تخزين ملفات', 'تصميم مكتبي', 'حلول تنظيمية']
    }
  ];

  const filteredCabinets = selectedCategory === 'all'
    ? cabinets
    : cabinets.filter(cabinet => cabinet.category === selectedCategory);

  return (
    <section id="cabinets" className="relative py-24 bg-gradient-to-br from-slate-50 via-white to-purple-50 overflow-hidden" ref={sectionRef}>
      {/* Background Decorations */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-purple-200/20 to-blue-200/20 rounded-full blur-3xl"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-br from-pink-200/20 to-orange-200/20 rounded-full blur-3xl"></div>
      </div>

      <div className="container mx-auto px-6 relative z-10">
        {/* Enhanced Header */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 50 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
          transition={{ duration: 0.8 }}
        >
          <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-purple-500 to-blue-500 rounded-2xl mb-6 shadow-lg">
            <i className="ri-archive-line text-2xl text-white"></i>
          </div>
          <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold bg-gradient-to-r from-gray-800 via-gray-900 to-gray-800 bg-clip-text text-transparent mb-6">
            معرض
            <span className="block bg-gradient-to-r from-purple-600 via-blue-600 to-indigo-600 bg-clip-text text-transparent">
              الخزائن الأنيقة
            </span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            تصاميم خزائن عصرية وأنيقة تجمع بين الجمال والوظيفة لتلبية احتياجات التخزين مع الحفاظ على جمالية المكان
          </p>
          <div className="w-24 h-1 bg-gradient-to-r from-purple-500 to-blue-500 mx-auto mt-8 rounded-full"></div>
        </motion.div>

        {/* Category Filter */}
        <motion.div
          className="flex flex-wrap justify-center gap-4 mb-12"
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.8, delay: 0.2 }}
        >
          {categories.map((category, index) => (
            <motion.button
              key={category.id}
              onClick={() => setSelectedCategory(category.id)}
              className={`group relative px-6 py-3 rounded-2xl font-medium transition-all duration-300 ${
                selectedCategory === category.id
                  ? 'bg-gradient-to-r from-purple-500 to-blue-500 text-white shadow-lg scale-105'
                  : 'bg-white/80 backdrop-blur-sm text-gray-700 hover:bg-white hover:scale-105 border border-gray-200'
              }`}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              initial={{ opacity: 0, y: 20 }}
              animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
            >
              <div className="flex items-center space-x-2 rtl:space-x-reverse">
                <i className={`${category.icon} text-lg`}></i>
                <span>{category.name}</span>
              </div>
              {selectedCategory !== category.id && (
                <div className="absolute inset-0 bg-gradient-to-r from-purple-500 to-blue-500 rounded-2xl opacity-0 group-hover:opacity-10 transition-opacity duration-300"></div>
              )}
            </motion.button>
          ))}
        </motion.div>

        {/* Main Swiper Gallery */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          className="mb-8 max-w-7xl mx-auto"
        >
          <Swiper
            modules={[Navigation, Pagination, Autoplay, Thumbs]}
            spaceBetween={30}
            slidesPerView={1}
            centeredSlides={false}
            effect="slide"
            autoplay={{
              delay: 4000,
              disableOnInteraction: false,
              pauseOnMouseEnter: true,
            }}
            navigation={{
              nextEl: '.swiper-button-next-custom',
              prevEl: '.swiper-button-prev-custom',
            }}
            pagination={{
              clickable: true,
              dynamicBullets: true,
            }}
            thumbs={{ swiper: thumbsSwiper }}
            loop={filteredCabinets.length > 2}
            breakpoints={{
              640: {
                slidesPerView: 1,
                spaceBetween: 20,
              },
              768: {
                slidesPerView: 2,
                spaceBetween: 30,
              },
              1024: {
                slidesPerView: 2,
                spaceBetween: 40,
              },
              1280: {
                slidesPerView: 2,
                spaceBetween: 50,
              },
            }}
            className="cabinet-swiper"
            style={{
              '--swiper-pagination-color': '#8b5cf6',
              '--swiper-pagination-bullet-inactive-color': '#d1d5db',
              '--swiper-pagination-bullet-size': '12px',
              '--swiper-pagination-bullet-horizontal-gap': '6px'
            }}
          >
            <AnimatePresence mode="wait">
              {filteredCabinets.map((cabinet, index) => (
                <SwiperSlide key={cabinet.id}>
                  <motion.div
                    className="group relative bg-white rounded-3xl overflow-hidden shadow-xl hover:shadow-2xl transition-all duration-500 cursor-pointer"
                    whileHover={{ y: -10 }}
                    onClick={() => setLightboxImage(cabinet)}
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    exit={{ opacity: 0, scale: 0.8 }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                  >
                    {/* Image Container */}
                    <div className="relative h-80 md:h-96 overflow-hidden">
                      <motion.img
                        src={cabinet.images[0]}
                        alt={cabinet.title}
                        className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700"
                        whileHover={{ scale: 1.1 }}
                      />

                      {/* Gradient Overlay */}
                      <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                      {/* Category Badge */}
                      <div className="absolute top-4 left-4 bg-white/90 backdrop-blur-sm text-gray-800 px-3 py-1 rounded-full text-xs font-medium">
                        {categories.find(cat => cat.id === cabinet.category)?.name}
                      </div>

                      {/* Zoom Icon */}
                      <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                        <div className="w-16 h-16 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center border border-white/30">
                          <i className="ri-zoom-in-line text-2xl text-white"></i>
                        </div>
                      </div>
                    </div>

                    {/* Content */}
                    <div className="p-6">
                      <h3 className="text-xl font-bold text-gray-800 mb-2 group-hover:text-purple-600 transition-colors duration-300">
                        {cabinet.title}
                      </h3>
                      <p className="text-gray-600 text-sm mb-4 leading-relaxed">
                        {cabinet.description}
                      </p>

                      {/* Features */}
                      <div className="flex flex-wrap gap-2 mb-4">
                        {cabinet.features.map((feature, idx) => (
                          <span
                            key={idx}
                            className="px-3 py-1 bg-gray-100 text-gray-700 text-xs rounded-full font-medium"
                          >
                            {feature}
                          </span>
                        ))}
                      </div>

                      {/* Action Button */}
                      <motion.button
                        className="w-full bg-gradient-to-r from-purple-500 to-blue-500 text-white py-3 rounded-xl font-medium hover:shadow-lg transition-all duration-300"
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        <div className="flex items-center justify-center space-x-2 rtl:space-x-reverse">
                          <i className="ri-eye-line"></i>
                          <span>عرض التفاصيل</span>
                        </div>
                      </motion.button>
                    </div>
                  </motion.div>
                </SwiperSlide>
              ))}
            </AnimatePresence>
          </Swiper>

          {/* Custom Navigation Buttons */}
          <div className="flex justify-center items-center space-x-4 rtl:space-x-reverse mt-8">
            <button className="swiper-button-prev-custom w-12 h-12 bg-white/80 backdrop-blur-sm rounded-full flex items-center justify-center shadow-lg hover:bg-white hover:scale-110 transition-all duration-300 border border-gray-200">
              <i className="ri-arrow-right-line text-xl text-gray-700"></i>
            </button>
            <button className="swiper-button-next-custom w-12 h-12 bg-white/80 backdrop-blur-sm rounded-full flex items-center justify-center shadow-lg hover:bg-white hover:scale-110 transition-all duration-300 border border-gray-200">
              <i className="ri-arrow-left-line text-xl text-gray-700"></i>
            </button>
          </div>
        </motion.div>

        {/* Thumbnails Swiper */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          className="mb-12"
        >
          <Swiper
            onSwiper={setThumbsSwiper}
            spaceBetween={10}
            slidesPerView={3}
            freeMode={true}
            watchSlidesProgress={true}
            centeredSlides={true}
            breakpoints={{
              640: {
                slidesPerView: 4,
                spaceBetween: 15,
              },
              768: {
                slidesPerView: 5,
                spaceBetween: 20,
              },
              1024: {
                slidesPerView: 6,
                spaceBetween: 20,
              },
              1280: {
                slidesPerView: 7,
                spaceBetween: 25,
              },
            }}
            className="thumbs-swiper"
          >
            {filteredCabinets.map((cabinet) => (
              <SwiperSlide key={`thumb-${cabinet.id}`}>
                <div className="relative h-20 md:h-24 rounded-xl overflow-hidden cursor-pointer group border-2 border-transparent hover:border-purple-500 transition-all duration-300">
                  <img
                    src={cabinet.images[0]}
                    alt={cabinet.title}
                    className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
                  />
                  <div className="absolute inset-0 bg-black/20 group-hover:bg-black/0 transition-colors duration-300"></div>
                </div>
              </SwiperSlide>
            ))}
          </Swiper>
        </motion.div>

        {/* Stats Section */}
        <motion.div
          className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-12"
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.8, delay: 0.8 }}
        >
          {[
            { number: '300+', label: 'خزانة منجزة', icon: 'ri-archive-line', color: 'from-purple-500 to-indigo-500' },
            { number: '12+', label: 'سنة خبرة', icon: 'ri-award-line', color: 'from-blue-500 to-cyan-500' },
            { number: '95%', label: 'رضا العملاء', icon: 'ri-heart-line', color: 'from-pink-500 to-rose-500' },
            { number: '24/7', label: 'دعم فني', icon: 'ri-customer-service-line', color: 'from-green-500 to-emerald-500' }
          ].map((stat, index) => (
            <motion.div
              key={index}
              className="text-center p-6 bg-white/80 backdrop-blur-sm rounded-2xl border border-white/50 hover:bg-white hover:scale-105 transition-all duration-300 shadow-lg"
              whileHover={{ y: -5 }}
              initial={{ opacity: 0, y: 20 }}
              animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
              transition={{ duration: 0.5, delay: 0.8 + index * 0.1 }}
            >
              <div className={`w-16 h-16 mx-auto mb-4 bg-gradient-to-r ${stat.color} rounded-2xl flex items-center justify-center shadow-lg`}>
                <i className={`${stat.icon} text-2xl text-white`}></i>
              </div>
              <div className={`text-3xl font-bold bg-gradient-to-r ${stat.color} bg-clip-text text-transparent mb-2`}>
                {stat.number}
              </div>
              <p className="text-gray-600 font-medium">{stat.label}</p>
            </motion.div>
          ))}
        </motion.div>

        {/* CTA Button */}
        <motion.div
          className="text-center"
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.8, delay: 1 }}
        >
          <motion.button
            className="bg-gradient-to-r from-purple-500 to-blue-500 hover:from-purple-600 hover:to-blue-600 text-white px-12 py-4 rounded-2xl font-bold text-lg shadow-xl hover:shadow-2xl transition-all duration-300"
            whileHover={{ scale: 1.05, y: -2 }}
            whileTap={{ scale: 0.95 }}
          >
            <div className="flex items-center space-x-3 rtl:space-x-reverse">
              <i className="ri-phone-line text-xl"></i>
              <span>احجز استشارة مجانية</span>
              <i className="ri-arrow-left-line text-xl"></i>
            </div>
          </motion.button>
        </motion.div>
      </div>

      {/* Enhanced Modal */}
      <AnimatePresence>
        {lightboxImage && (
          <motion.div
            className="fixed inset-0 bg-black/90 backdrop-blur-sm z-50 flex items-center justify-center p-4"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={() => {
              setLightboxImage(null);
              setModalImageIndex(0);
            }}
          >
            {/* Mobile Modal */}
            <motion.div
              className="relative w-full max-w-4xl h-[90vh] bg-white rounded-3xl overflow-hidden shadow-2xl flex flex-col lg:hidden"
              initial={{ scale: 0.5, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.5, opacity: 0 }}
              transition={{ type: "spring", damping: 25, stiffness: 300 }}
              onClick={(e) => e.stopPropagation()}
            >
              {/* Close Button */}
              <button
                onClick={() => {
                  setLightboxImage(null);
                  setModalImageIndex(0);
                }}
                className="absolute top-4 right-4 z-10 w-12 h-12 bg-black/50 backdrop-blur-sm rounded-full flex items-center justify-center text-white hover:bg-black/70 transition-colors duration-300"
              >
                <i className="ri-close-line text-xl"></i>
              </button>

              {/* Image Slider */}
              <div className="relative flex-1">
                <Swiper
                  modules={[Navigation, Pagination]}
                  spaceBetween={0}
                  slidesPerView={1}
                  navigation={{
                    nextEl: '.modal-swiper-button-next',
                    prevEl: '.modal-swiper-button-prev',
                  }}
                  pagination={{
                    clickable: true,
                    dynamicBullets: true,
                  }}
                  onSlideChange={(swiper) => setModalImageIndex(swiper.activeIndex)}
                  className="modal-swiper h-full"
                  style={{
                    '--swiper-pagination-color': '#8b5cf6',
                    '--swiper-pagination-bullet-inactive-color': '#d1d5db',
                  }}
                >
                  {lightboxImage.images.map((image, index) => (
                    <SwiperSlide key={index}>
                      <div className="relative h-full">
                        <img
                          src={image}
                          alt={`${lightboxImage.title} - صورة ${index + 1}`}
                          className="w-full h-full object-cover"
                        />
                      </div>
                    </SwiperSlide>
                  ))}
                </Swiper>

                {/* Custom Navigation Buttons */}
                {lightboxImage.images.length > 1 && (
                  <>
                    <button className="modal-swiper-button-prev absolute left-4 top-1/2 -translate-y-1/2 z-10 w-12 h-12 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center text-white hover:bg-white/30 transition-all duration-300">
                      <i className="ri-arrow-right-line text-xl"></i>
                    </button>
                    <button className="modal-swiper-button-next absolute right-4 top-1/2 -translate-y-1/2 z-10 w-12 h-12 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center text-white hover:bg-white/30 transition-all duration-300">
                      <i className="ri-arrow-left-line text-xl"></i>
                    </button>
                  </>
                )}

                {/* Image Counter */}
                {lightboxImage.images.length > 1 && (
                  <div className="absolute bottom-4 left-4 bg-black/50 backdrop-blur-sm text-white px-3 py-1 rounded-full text-sm">
                    {modalImageIndex + 1} / {lightboxImage.images.length}
                  </div>
                )}
              </div>

              {/* Content Section */}
              <div className="p-6 bg-gradient-to-r from-gray-50 to-white">
                <div className="text-center mb-6">
                  <h3 className="text-2xl font-bold text-gray-800 mb-4">
                    {lightboxImage.title}
                  </h3>
                  <p className="text-gray-600 leading-relaxed text-base">
                    {lightboxImage.description}
                  </p>
                </div>

                {/* Contact Section */}
                <div className="text-center">
                  <h4 className="text-xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent mb-6">
                    اطلب الآن
                  </h4>

                  {/* Social Media Icons */}
                  <div className="flex justify-center items-center gap-4 flex-wrap">
                    {/* WhatsApp */}
                    <motion.a
                      href="https://wa.me/966500000000"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="group flex items-center justify-center w-12 h-12 bg-gradient-to-r from-green-500 to-green-600 rounded-full text-white shadow-lg hover:shadow-xl transition-all duration-300"
                      whileHover={{ scale: 1.1, y: -2 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <i className="ri-whatsapp-line text-xl"></i>
                    </motion.a>

                    {/* Instagram */}
                    <motion.a
                      href="https://instagram.com/expertwonders"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="group flex items-center justify-center w-12 h-12 bg-gradient-to-r from-pink-500 to-purple-600 rounded-full text-white shadow-lg hover:shadow-xl transition-all duration-300"
                      whileHover={{ scale: 1.1, y: -2 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <i className="ri-instagram-line text-xl"></i>
                    </motion.a>

                    {/* Twitter */}
                    <motion.a
                      href="https://twitter.com/expertwonders"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="group flex items-center justify-center w-12 h-12 bg-gradient-to-r from-blue-400 to-blue-600 rounded-full text-white shadow-lg hover:shadow-xl transition-all duration-300"
                      whileHover={{ scale: 1.1, y: -2 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <i className="ri-twitter-line text-xl"></i>
                    </motion.a>

                    {/* Snapchat */}
                    <motion.a
                      href="https://snapchat.com/add/expertwonders"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="group flex items-center justify-center w-12 h-12 bg-gradient-to-r from-yellow-400 to-yellow-500 rounded-full text-white shadow-lg hover:shadow-xl transition-all duration-300"
                      whileHover={{ scale: 1.1, y: -2 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <i className="ri-snapchat-line text-lg"></i>
                    </motion.a>

                    {/* TikTok */}
                    <motion.a
                      href="https://tiktok.com/@expertwonders"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="group flex items-center justify-center w-12 h-12 bg-gradient-to-r from-gray-800 to-black rounded-full text-white shadow-lg hover:shadow-xl transition-all duration-300"
                      whileHover={{ scale: 1.1, y: -2 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <i className="ri-tiktok-line text-xl"></i>
                    </motion.a>
                  </div>

                  {/* Contact Text */}
                  <p className="text-gray-500 text-sm mt-4">
                    تواصل معنا عبر أي من منصات التواصل الاجتماعي للحصول على استشارة مجانية
                  </p>
                </div>
              </div>
            </motion.div>

            {/* Desktop Modal */}
            <motion.div
              className="relative w-full max-w-5xl h-[80vh] bg-white rounded-3xl overflow-hidden shadow-2xl hidden lg:grid lg:grid-cols-3"
              initial={{ scale: 0.5, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.5, opacity: 0 }}
              transition={{ type: "spring", damping: 25, stiffness: 300 }}
              onClick={(e) => e.stopPropagation()}
            >
              {/* Close Button */}
              <button
                onClick={() => {
                  setLightboxImage(null);
                  setModalImageIndex(0);
                }}
                className="absolute top-4 right-4 z-10 w-10 h-10 bg-black/50 backdrop-blur-sm rounded-full flex items-center justify-center text-white hover:bg-black/70 transition-colors duration-300"
              >
                <i className="ri-close-line text-lg"></i>
              </button>

              {/* Left Column - Main Image */}
              <div className="col-span-2 relative">
                <div className="h-full relative">
                  <img
                    src={lightboxImage.images[modalImageIndex]}
                    alt={`${lightboxImage.title} - صورة ${modalImageIndex + 1}`}
                    className="w-full h-full object-cover"
                  />

                  {/* Navigation Buttons */}
                  {lightboxImage.images.length > 1 && (
                    <>
                      <button
                        onClick={() => setModalImageIndex(modalImageIndex > 0 ? modalImageIndex - 1 : lightboxImage.images.length - 1)}
                        className="absolute left-4 top-1/2 -translate-y-1/2 w-10 h-10 bg-black/50 backdrop-blur-sm rounded-full flex items-center justify-center text-white hover:bg-black/70 transition-all duration-300"
                      >
                        <i className="ri-arrow-right-line text-lg"></i>
                      </button>
                      <button
                        onClick={() => setModalImageIndex(modalImageIndex < lightboxImage.images.length - 1 ? modalImageIndex + 1 : 0)}
                        className="absolute right-4 top-1/2 -translate-y-1/2 w-10 h-10 bg-black/50 backdrop-blur-sm rounded-full flex items-center justify-center text-white hover:bg-black/70 transition-all duration-300"
                      >
                        <i className="ri-arrow-left-line text-lg"></i>
                      </button>
                    </>
                  )}

                  {/* Image Counter */}
                  {lightboxImage.images.length > 1 && (
                    <div className="absolute bottom-4 left-4 bg-black/50 backdrop-blur-sm text-white px-3 py-1 rounded-full text-sm">
                      {modalImageIndex + 1} / {lightboxImage.images.length}
                    </div>
                  )}
                </div>
              </div>

              {/* Right Column - Content */}
              <div className="bg-gradient-to-b from-gray-50 to-white p-6 flex flex-col justify-between">
                {/* Top Section - Title and Description */}
                <div>
                  <h3 className="text-2xl font-bold text-gray-800 mb-4">
                    {lightboxImage.title}
                  </h3>
                  <p className="text-gray-600 leading-relaxed text-sm mb-6">
                    {lightboxImage.description}
                  </p>

                  {/* Thumbnail Images */}
                  {lightboxImage.images.length > 1 && (
                    <div className="mb-6">
                      <div className="grid grid-cols-3 gap-2">
                        {lightboxImage.images.map((image, index) => (
                          <button
                            key={index}
                            onClick={() => setModalImageIndex(index)}
                            className={`relative h-16 rounded-lg overflow-hidden border-2 transition-all duration-300 ${
                              modalImageIndex === index
                                ? 'border-purple-500 scale-105'
                                : 'border-gray-200 hover:border-purple-300'
                            }`}
                          >
                            <img
                              src={image}
                              alt={`${lightboxImage.title} - صورة ${index + 1}`}
                              className="w-full h-full object-cover"
                            />
                          </button>
                        ))}
                      </div>
                    </div>
                  )}
                </div>

                {/* Bottom Section - Contact */}
                <div>
                  <h4 className="text-lg font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent mb-4 text-center">
                    اطلب الآن
                  </h4>

                  {/* Social Media Icons */}
                  <div className="grid grid-cols-3 gap-3 mb-4">
                    {/* WhatsApp */}
                    <motion.a
                      href="https://wa.me/966500000000"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center justify-center h-12 bg-gradient-to-r from-green-500 to-green-600 rounded-xl text-white shadow-lg hover:shadow-xl transition-all duration-300"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <i className="ri-whatsapp-line text-lg"></i>
                    </motion.a>

                    {/* Instagram */}
                    <motion.a
                      href="https://instagram.com/expertwonders"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center justify-center h-12 bg-gradient-to-r from-pink-500 to-purple-600 rounded-xl text-white shadow-lg hover:shadow-xl transition-all duration-300"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <i className="ri-instagram-line text-lg"></i>
                    </motion.a>

                    {/* Twitter */}
                    <motion.a
                      href="https://twitter.com/expertwonders"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center justify-center h-12 bg-gradient-to-r from-blue-400 to-blue-600 rounded-xl text-white shadow-lg hover:shadow-xl transition-all duration-300"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <i className="ri-twitter-line text-lg"></i>
                    </motion.a>

                    {/* Snapchat */}
                    <motion.a
                      href="https://snapchat.com/add/expertwonders"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center justify-center h-12 bg-gradient-to-r from-yellow-400 to-yellow-500 rounded-xl text-white shadow-lg hover:shadow-xl transition-all duration-300"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <i className="ri-snapchat-line text-lg"></i>
                    </motion.a>

                    {/* TikTok */}
                    <motion.a
                      href="https://tiktok.com/@expertwonders"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center justify-center h-12 bg-gradient-to-r from-gray-800 to-black rounded-xl text-white shadow-lg hover:shadow-xl transition-all duration-300"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <i className="ri-tiktok-line text-lg"></i>
                    </motion.a>

                    {/* Phone */}
                    <motion.a
                      href="tel:+966500000000"
                      className="flex items-center justify-center h-12 bg-gradient-to-r from-emerald-500 to-teal-600 rounded-xl text-white shadow-lg hover:shadow-xl transition-all duration-300"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <i className="ri-phone-line text-lg"></i>
                    </motion.a>
                  </div>

                  {/* Contact Text */}
                  <p className="text-gray-500 text-xs text-center">
                    تواصل معنا للحصول على استشارة مجانية
                  </p>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </section>
  );
};

export default CabinetGallery;
